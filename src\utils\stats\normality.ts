import { normalityTestService } from '../services/normalityTestService';

/**
 * Comprehensive Normality Testing Module
 * 
 * This module provides multiple normality tests commonly used in statistical analysis:
 * - Shapiro-<PERSON>ilk Test (primary for n < 50)
 * - Kolmogorov-Smirnov Test (good for larger samples)
 * - Jarque-Bera Test (based on skewness and kurtosis)
 * - Anderson-Darling Test (sensitive to tail deviations)
 */

export interface NormalityTestResult {
  testName: string;
  statistic: number;
  pValue: number;
  isNormal: boolean;
  alpha: number;
  sampleSize: number;
  interpretation: string;
  recommendation?: string;
}

export interface ComprehensiveNormalityResult {
  sampleSize: number;
  recommendedTest: string;
  tests: {
    shapiroWilk?: NormalityTestResult;
    kolmogorovSmirnov?: NormalityTestResult;
    jarqueBera?: NormalityTestResult;
    andersonDarling?: NormalityTestResult;
  };
  overallAssessment: {
    isNormal: boolean;
    confidence: 'high' | 'medium' | 'low';
    summary: string;
  };
}

/**
 * Shapiro-Wilk Test for Normality
 * Most powerful test for small to medium sample sizes (n ≤ 50)
 */
export const shapiroWilkTest = async (data: number[], alpha: number = 0.05): Promise<NormalityTestResult> => {
  try {
    return await normalityTestService.shapiroWilkTest(data, alpha);
  } catch (error) {
    console.error('Error in Shapiro-Wilk test:', error);
    return {
      testName: 'Shapiro-Wilk',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: data.length,
      interpretation: `Error in Shapiro-Wilk test: ${error}`,
      recommendation: 'Check data quality and try again'
    };
  }
};

/**
 * Kolmogorov-Smirnov Test for Normality
 * Good for larger sample sizes, tests overall distribution shape
 */
export const kolmogorovSmirnovTest = async (data: number[], alpha: number = 0.05): Promise<NormalityTestResult> => {
  try {
    return await normalityTestService.kolmogorovSmirnovTest(data, alpha);
  } catch (error) {
    console.error('Error in Kolmogorov-Smirnov test:', error);
    return {
      testName: 'Kolmogorov-Smirnov',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: data.length,
      interpretation: `Error in Kolmogorov-Smirnov test: ${error}`,
      recommendation: 'Check data quality and try again'
    };
  }
};

/**
 * Jarque-Bera Test for Normality
 * Based on skewness and kurtosis, good for detecting specific types of non-normality
 */
export const jarqueBeraTest = async (data: number[], alpha: number = 0.05): Promise<NormalityTestResult> => {
  try {
    return await normalityTestService.jarqueBeraTest(data, alpha);
  } catch (error) {
    console.error('Error in Jarque-Bera test:', error);
    return {
      testName: 'Jarque-Bera',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: data.length,
      interpretation: `Error in Jarque-Bera test: ${error}`,
      recommendation: 'Check data quality and try again'
    };
  }
};

// All helper functions have been replaced by Pyodide-based Python implementations



/**
 * Anderson-Darling Test for Normality
 * More sensitive to deviations in the tails compared to KS test
 */
export const andersonDarlingTest = async (data: number[], alpha: number = 0.05): Promise<NormalityTestResult> => {
  try {
    return await normalityTestService.andersonDarlingTest(data, alpha);
  } catch (error) {
    console.error('Error in Anderson-Darling test:', error);
    return {
      testName: 'Anderson-Darling',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: data.length,
      interpretation: `Error in Anderson-Darling test: ${error}`,
      recommendation: 'Check data quality and try again'
    };
  }
};



/**
 * Comprehensive Normality Testing
 * Runs multiple normality tests and provides an overall assessment
 */
export const comprehensiveNormalityTest = async (
  data: number[],
  alpha: number = 0.05,
  testsToRun: string[] = ['auto']
): Promise<ComprehensiveNormalityResult> => {
  const n = data.length;

  // Determine which tests to run
  let selectedTests: string[];
  if (testsToRun.includes('auto')) {
    if (n <= 50) {
      selectedTests = ['shapiroWilk', 'jarqueBera'];
    } else if (n <= 5000) {
      selectedTests = ['kolmogorovSmirnov', 'jarqueBera', 'andersonDarling'];
    } else {
      selectedTests = ['jarqueBera'];
    }
  } else {
    selectedTests = testsToRun;
  }

  // Determine recommended test
  let recommendedTest: string;
  if (n <= 50) {
    recommendedTest = 'Shapiro-Wilk';
  } else if (n <= 5000) {
    recommendedTest = 'Kolmogorov-Smirnov';
  } else {
    recommendedTest = 'Jarque-Bera';
  }

  // Run selected tests
  const tests: any = {};

  if (selectedTests.includes('shapiroWilk')) {
    tests.shapiroWilk = await shapiroWilkTest(data, alpha);
  }

  if (selectedTests.includes('kolmogorovSmirnov')) {
    tests.kolmogorovSmirnov = await kolmogorovSmirnovTest(data, alpha);
  }

  if (selectedTests.includes('jarqueBera')) {
    tests.jarqueBera = await jarqueBeraTest(data, alpha);
  }

  if (selectedTests.includes('andersonDarling')) {
    tests.andersonDarling = await andersonDarlingTest(data, alpha);
  }

  // Overall assessment
  const testResults = Object.values(tests) as NormalityTestResult[];
  const validTests = testResults.filter(test => !isNaN(test.pValue));

  if (validTests.length === 0) {
    return {
      sampleSize: n,
      recommendedTest,
      tests,
      overallAssessment: {
        isNormal: false,
        confidence: 'low',
        summary: 'Unable to assess normality - insufficient data or all tests failed'
      }
    };
  }

  const normalTests = validTests.filter(test => test.isNormal);
  const normalRatio = normalTests.length / validTests.length;

  let isNormal: boolean;
  let confidence: 'high' | 'medium' | 'low';
  let summary: string;

  if (normalRatio === 1) {
    isNormal = true;
    confidence = validTests.length >= 2 ? 'high' : 'medium';
    summary = `All ${validTests.length} test(s) indicate normal distribution`;
  } else if (normalRatio >= 0.5) {
    isNormal = true;
    confidence = 'medium';
    summary = `${normalTests.length} of ${validTests.length} tests indicate normal distribution`;
  } else {
    isNormal = false;
    confidence = validTests.length >= 2 ? 'high' : 'medium';
    summary = `${validTests.length - normalTests.length} of ${validTests.length} tests indicate non-normal distribution`;
  }

  return {
    sampleSize: n,
    recommendedTest,
    tests,
    overallAssessment: {
      isNormal,
      confidence,
      summary
    }
  };
};

/**
 * Legacy function for backward compatibility
 * Maps to the comprehensive testing with automatic test selection
 */
export const isNormallyDistributed = async (data: number[]): Promise<{
  isNormal: boolean;
  pValue: number;
  statistic: number;
}> => {
  try {
    return await normalityTestService.isNormallyDistributed(data);
  } catch (error) {
    console.error('Error in normality test:', error);
    return {
      isNormal: false,
      pValue: NaN,
      statistic: NaN
    };
  }
};

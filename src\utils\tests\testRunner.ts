/**
 * Test Runner for Normality Tests
 * 
 * This module provides a simple way to run validation tests
 * for the Pyodide-based normality testing implementation.
 * 
 * Usage in browser console:
 * import { runNormalityTestValidation } from './utils/tests/testRunner';
 * runNormalityTestValidation();
 */

import { runAllValidationTests } from './normalityTestValidation';

/**
 * Run normality test validation and log results to console
 */
export const runNormalityTestValidation = async (): Promise<void> => {
  console.log('🧪 Starting Pyodide-based Normality Test Validation...');
  console.log('=' .repeat(60));
  
  try {
    const results = await runAllValidationTests();
    
    if (results.success) {
      console.log('✅ All validation tests PASSED!');
      console.log('🎉 Pyodide-based normality tests are working correctly.');
    } else {
      console.log('❌ Some validation tests FAILED!');
      console.log('⚠️  There may be issues with the normality test implementation.');
    }
    
    console.log('\n📊 Detailed Results:');
    console.log('=' .repeat(40));
    
    // Log Shapiro-Wilk accuracy test
    const shapiroTest = results.details.shapiroWilkAccuracy;
    console.log(`\n🔬 Shapiro-Wilk Accuracy Test:`);
    console.log(`   Status: ${shapiroTest.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Message: ${shapiroTest.message}`);
    if (shapiroTest.details.statistic) {
      console.log(`   W Statistic: ${shapiroTest.details.statistic.toFixed(4)}`);
      console.log(`   P-value: ${shapiroTest.details.pValue.toFixed(4)}`);
      console.log(`   Is Normal: ${shapiroTest.details.isNormal}`);
    }
    
    // Log all tests validation
    const allTestsTest = results.details.allNormalityTests;
    console.log(`\n🧮 All Normality Tests Validation:`);
    console.log(`   Status: ${allTestsTest.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Message: ${allTestsTest.message}`);
    
    if (allTestsTest.details) {
      console.log('\n   📈 Normal Data Results:');
      const normalResults = allTestsTest.details.normalData;
      console.log(`     Shapiro-Wilk: p=${normalResults.shapiroWilk.pValue.toFixed(4)}, normal=${normalResults.shapiroWilk.isNormal}`);
      console.log(`     Kolmogorov-Smirnov: p=${normalResults.kolmogorovSmirnov.pValue.toFixed(4)}, normal=${normalResults.kolmogorovSmirnov.isNormal}`);
      console.log(`     Jarque-Bera: p=${normalResults.jarqueBera.pValue.toFixed(4)}, normal=${normalResults.jarqueBera.isNormal}`);
      console.log(`     Anderson-Darling: p=${normalResults.andersonDarling.pValue.toFixed(4)}, normal=${normalResults.andersonDarling.isNormal}`);
      
      console.log('\n   📉 Non-Normal Data Results:');
      const nonNormalResults = allTestsTest.details.nonNormalData;
      console.log(`     Shapiro-Wilk: p=${nonNormalResults.shapiroWilk.pValue.toFixed(4)}, normal=${nonNormalResults.shapiroWilk.isNormal}`);
      console.log(`     Kolmogorov-Smirnov: p=${nonNormalResults.kolmogorovSmirnov.pValue.toFixed(4)}, normal=${nonNormalResults.kolmogorovSmirnov.isNormal}`);
      console.log(`     Jarque-Bera: p=${nonNormalResults.jarqueBera.pValue.toFixed(4)}, normal=${nonNormalResults.jarqueBera.isNormal}`);
      console.log(`     Anderson-Darling: p=${nonNormalResults.andersonDarling.pValue.toFixed(4)}, normal=${nonNormalResults.andersonDarling.isNormal}`);
    }
    
    console.log('\n' + '=' .repeat(60));
    
    if (results.success) {
      console.log('🎯 Conclusion: The Pyodide-based normality tests are functioning correctly!');
      console.log('   The previous Shapiro-Wilk p-value calculation issues have been resolved.');
    } else {
      console.log('⚠️  Conclusion: There are still issues that need to be addressed.');
      console.log('   Please review the test results above for specific failures.');
    }
    
  } catch (error) {
    console.error('💥 Error running validation tests:', error);
    console.log('❌ Validation tests could not be completed.');
    console.log('   This might indicate a problem with Pyodide initialization or the test setup.');
  }
};

/**
 * Quick test function for browser console
 * Usage: window.testNormality()
 */
export const setupGlobalTestFunction = (): void => {
  if (typeof window !== 'undefined') {
    (window as any).testNormality = runNormalityTestValidation;
    console.log('🔧 Global test function setup complete.');
    console.log('   You can now run: testNormality() in the browser console.');
  }
};

/**
 * Test a specific dataset for normality
 */
export const testDatasetNormality = async (data: number[]): Promise<void> => {
  console.log(`🔍 Testing normality for dataset with ${data.length} observations...`);
  
  try {
    // Import the service
    const { normalityTestService } = await import('../services/normalityTestService');
    
    // Run comprehensive test
    const result = await normalityTestService.comprehensiveNormalityTest(data, 0.05, ['auto']);
    
    console.log('\n📊 Normality Test Results:');
    console.log('=' .repeat(40));
    console.log(`Sample Size: ${result.sampleSize}`);
    console.log(`Recommended Test: ${result.recommendedTest}`);
    console.log(`Overall Assessment: ${result.overallAssessment.isNormal ? 'Normal' : 'Non-normal'}`);
    console.log(`Confidence: ${result.overallAssessment.confidence}`);
    console.log(`Summary: ${result.overallAssessment.summary}`);
    
    console.log('\n🧪 Individual Test Results:');
    Object.entries(result.tests).forEach(([testName, testResult]: [string, any]) => {
      console.log(`  ${testName}:`);
      console.log(`    Statistic: ${testResult.statistic.toFixed(4)}`);
      console.log(`    P-value: ${testResult.pValue.toFixed(4)}`);
      console.log(`    Is Normal: ${testResult.isNormal}`);
      console.log(`    Interpretation: ${testResult.interpretation}`);
    });
    
  } catch (error) {
    console.error('💥 Error testing dataset normality:', error);
  }
};

// Auto-setup global function if in browser environment
if (typeof window !== 'undefined') {
  setupGlobalTestFunction();
}

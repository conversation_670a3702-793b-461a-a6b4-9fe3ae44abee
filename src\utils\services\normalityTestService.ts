import { NormalityTestResult, ComprehensiveNormalityResult } from '../stats/normality';

/**
 * Pyodide-based Normality Testing Service
 * 
 * This service provides accurate normality tests using Python's scipy.stats library
 * through Pyodide, replacing the problematic JavaScript implementations.
 */
class NormalityTestService {
  private pyodide: any = null;
  private isInitialized: boolean = false;
  private initializationPromise: Promise<void> | null = null;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    if (this.initializationPromise) return this.initializationPromise;

    this.initializationPromise = this.doInitialize();
    return this.initializationPromise;
  }

  private async doInitialize(): Promise<void> {
    try {
      console.log('🔬 Initializing Pyodide for normality testing...');

      // Load Pyodide from CDN
      if (typeof window !== 'undefined' && !(window as any).loadPyodide) {
        console.log('📦 Loading Pyodide script...');
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js';
        document.head.appendChild(script);

        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
        });
        console.log('✅ Pyodide script loaded successfully');
      }

      // Initialize Pyodide
      console.log('🚀 Initializing Pyodide environment...');
      this.pyodide = await (window as any).loadPyodide({
        indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/'
      });
      console.log('✅ Pyodide environment initialized');

      // Install required packages
      console.log('📦 Installing Python packages (numpy, scipy)...');
      await this.pyodide.loadPackage(['numpy', 'scipy']);
      console.log('✅ Python packages installed successfully');

      console.log('🔧 Setting up normality test implementations...');
      this.setupNormalityTestImplementation();
      console.log('✅ Pyodide normality testing service ready!');

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Pyodide:', error);
      throw new Error('Failed to initialize Python environment for normality testing');
    }
  }

  private setupNormalityTestImplementation(): void {
    this.pyodide.runPython(`
import numpy as np
import scipy.stats as stats
import json
import warnings
warnings.filterwarnings('ignore')

def shapiro_wilk_test(data, alpha=0.05):
    """
    Perform Shapiro-Wilk test for normality using scipy.stats.shapiro
    """
    try:
        data = np.array(data, dtype=float)
        
        # Remove NaN values
        data = data[~np.isnan(data)]
        
        if len(data) < 3:
            return {
                'testName': 'Shapiro-Wilk',
                'statistic': float('nan'),
                'pValue': float('nan'),
                'isNormal': bool(False),
                'alpha': float(alpha),
                'sampleSize': int(len(data)),
                'interpretation': str('Insufficient data for Shapiro-Wilk test (minimum 3 observations required)'),
                'recommendation': str('Collect more data points')
            }
        
        if len(data) > 5000:
            return {
                'testName': 'Shapiro-Wilk',
                'statistic': float('nan'),
                'pValue': float('nan'),
                'isNormal': bool(False),
                'alpha': alpha,
                'sampleSize': len(data),
                'interpretation': 'Sample size too large for Shapiro-Wilk test (maximum 5000 observations)',
                'recommendation': 'Use Kolmogorov-Smirnov or Jarque-Bera test for larger samples'
            }
        
        # Perform Shapiro-Wilk test
        statistic, p_value = stats.shapiro(data)
        
        is_normal = p_value >= alpha
        
        interpretation = (
            f"Data appears normally distributed (p = {p_value:.4f} ≥ {alpha})" if is_normal
            else f"Data significantly deviates from normal distribution (p = {p_value:.4f} < {alpha})"
        )
        
        recommendation = (
            'Shapiro-Wilk is the recommended test for this sample size' if len(data) <= 50
            else 'Consider using other tests for larger samples'
        )
        
        return {
            'testName': 'Shapiro-Wilk',
            'statistic': float(statistic),
            'pValue': float(p_value),
            'isNormal': bool(is_normal),
            'alpha': float(alpha),
            'sampleSize': int(len(data)),
            'interpretation': str(interpretation),
            'recommendation': str(recommendation)
        }
        
    except Exception as e:
        return {
            'testName': 'Shapiro-Wilk',
            'statistic': float('nan'),
            'pValue': float('nan'),
            'isNormal': False,
            'alpha': alpha,
            'sampleSize': len(data) if 'data' in locals() else 0,
            'interpretation': f'Error in Shapiro-Wilk test: {str(e)}',
            'recommendation': 'Check data quality and try again'
        }

def kolmogorov_smirnov_test(data, alpha=0.05):
    """
    Perform Kolmogorov-Smirnov test for normality with Lilliefors correction
    """
    try:
        data = np.array(data, dtype=float)
        
        # Remove NaN values
        data = data[~np.isnan(data)]
        
        if len(data) < 5:
            return {
                'testName': 'Kolmogorov-Smirnov',
                'statistic': float('nan'),
                'pValue': float('nan'),
                'isNormal': False,
                'alpha': alpha,
                'sampleSize': len(data),
                'interpretation': 'Insufficient data for Kolmogorov-Smirnov test (minimum 5 observations required)',
                'recommendation': 'Collect more data points'
            }
        
        # Standardize the data
        mean = np.mean(data)
        std = np.std(data, ddof=1)
        
        if std == 0:
            return {
                'testName': 'Kolmogorov-Smirnov',
                'statistic': 0.0,
                'pValue': 1.0,
                'isNormal': True,
                'alpha': alpha,
                'sampleSize': len(data),
                'interpretation': 'Data has zero variance (all values identical)',
                'recommendation': 'Data is degenerate; normality test not meaningful'
            }
        
        # Perform KS test against normal distribution with estimated parameters
        statistic, p_value = stats.kstest(data, lambda x: stats.norm.cdf(x, mean, std))
        
        # Apply Lilliefors correction (approximate)
        n = len(data)
        corrected_p_value = p_value * (1 + 0.12/np.sqrt(n) + 0.11/n)
        corrected_p_value = min(corrected_p_value, 1.0)
        
        is_normal = corrected_p_value >= alpha
        
        interpretation = (
            f"Data appears normally distributed (p = {corrected_p_value:.4f} ≥ {alpha})" if is_normal
            else f"Data significantly deviates from normal distribution (p = {corrected_p_value:.4f} < {alpha})"
        )
        
        return {
            'testName': 'Kolmogorov-Smirnov',
            'statistic': float(statistic),
            'pValue': float(corrected_p_value),
            'isNormal': bool(is_normal),
            'alpha': float(alpha),
            'sampleSize': int(len(data)),
            'interpretation': str(interpretation),
            'recommendation': 'Good for larger samples; less powerful than Shapiro-Wilk for small samples'
        }
        
    except Exception as e:
        return {
            'testName': 'Kolmogorov-Smirnov',
            'statistic': float('nan'),
            'pValue': float('nan'),
            'isNormal': False,
            'alpha': alpha,
            'sampleSize': len(data) if 'data' in locals() else 0,
            'interpretation': f'Error in Kolmogorov-Smirnov test: {str(e)}',
            'recommendation': 'Check data quality and try again'
        }

def jarque_bera_test(data, alpha=0.05):
    """
    Perform Jarque-Bera test for normality using scipy.stats.jarque_bera
    """
    try:
        data = np.array(data, dtype=float)
        
        # Remove NaN values
        data = data[~np.isnan(data)]
        
        if len(data) < 8:
            return {
                'testName': 'Jarque-Bera',
                'statistic': float('nan'),
                'pValue': float('nan'),
                'isNormal': False,
                'alpha': alpha,
                'sampleSize': len(data),
                'interpretation': 'Insufficient data for Jarque-Bera test (minimum 8 observations required)',
                'recommendation': 'Collect more data points'
            }
        
        # Perform Jarque-Bera test
        statistic, p_value = stats.jarque_bera(data)
        
        is_normal = p_value >= alpha
        
        interpretation = (
            f"Data appears normally distributed (p = {p_value:.4f} ≥ {alpha})" if is_normal
            else f"Data significantly deviates from normal distribution (p = {p_value:.4f} < {alpha})"
        )
        
        return {
            'testName': 'Jarque-Bera',
            'statistic': float(statistic),
            'pValue': float(p_value),
            'isNormal': bool(is_normal),
            'alpha': float(alpha),
            'sampleSize': int(len(data)),
            'interpretation': str(interpretation),
            'recommendation': 'Based on skewness and kurtosis; good for large samples'
        }
        
    except Exception as e:
        return {
            'testName': 'Jarque-Bera',
            'statistic': float('nan'),
            'pValue': float('nan'),
            'isNormal': False,
            'alpha': alpha,
            'sampleSize': len(data) if 'data' in locals() else 0,
            'interpretation': f'Error in Jarque-Bera test: {str(e)}',
            'recommendation': 'Check data quality and try again'
        }

def anderson_darling_test(data, alpha=0.05):
    """
    Perform Anderson-Darling test for normality using scipy.stats.anderson
    """
    try:
        data = np.array(data, dtype=float)
        
        # Remove NaN values
        data = data[~np.isnan(data)]
        
        if len(data) < 5:
            return {
                'testName': 'Anderson-Darling',
                'statistic': float('nan'),
                'pValue': float('nan'),
                'isNormal': False,
                'alpha': alpha,
                'sampleSize': len(data),
                'interpretation': 'Insufficient data for Anderson-Darling test (minimum 5 observations required)',
                'recommendation': 'Collect more data points'
            }
        
        # Perform Anderson-Darling test
        result = stats.anderson(data, dist='norm')
        statistic = result.statistic
        critical_values = result.critical_values
        significance_levels = result.significance_level
        
        # Find the appropriate critical value for the given alpha
        alpha_percent = alpha * 100
        
        # Default to most conservative test if exact alpha not available
        is_normal = True
        critical_value = critical_values[-1]  # Most stringent
        
        for i, sig_level in enumerate(significance_levels):
            if abs(sig_level - alpha_percent) < 0.1:  # Close match
                critical_value = critical_values[i]
                break
            elif sig_level >= alpha_percent:
                critical_value = critical_values[i]
                break
        
        is_normal = statistic <= critical_value
        
        # Approximate p-value (Anderson-Darling doesn't provide exact p-values)
        if statistic <= critical_values[0]:
            p_value = 0.25
        elif statistic <= critical_values[1]:
            p_value = 0.10
        elif statistic <= critical_values[2]:
            p_value = 0.05
        elif statistic <= critical_values[3]:
            p_value = 0.025
        elif statistic <= critical_values[4]:
            p_value = 0.01
        else:
            p_value = 0.001
        
        interpretation = (
            f"Data appears normally distributed (statistic = {statistic:.4f} ≤ {critical_value:.4f})" if is_normal
            else f"Data significantly deviates from normal distribution (statistic = {statistic:.4f} > {critical_value:.4f})"
        )
        
        return {
            'testName': 'Anderson-Darling',
            'statistic': float(statistic),
            'pValue': float(p_value),
            'isNormal': bool(is_normal),
            'alpha': float(alpha),
            'sampleSize': int(len(data)),
            'interpretation': str(interpretation),
            'recommendation': 'More sensitive to deviations in the tails than KS test'
        }
        
    except Exception as e:
        return {
            'testName': 'Anderson-Darling',
            'statistic': float('nan'),
            'pValue': float('nan'),
            'isNormal': False,
            'alpha': alpha,
            'sampleSize': len(data) if 'data' in locals() else 0,
            'interpretation': f'Error in Anderson-Darling test: {str(e)}',
            'recommendation': 'Check data quality and try again'
        }
def comprehensive_normality_test(data, alpha=0.05, tests_to_run=['auto']):
    """
    Run comprehensive normality testing with multiple tests
    """
    try:
        data = np.array(data, dtype=float)

        # Remove NaN values
        data = data[~np.isnan(data)]
        n = len(data)

        if n < 3:
            return {
                'sampleSize': n,
                'recommendedTest': 'None',
                'tests': {},
                'overallAssessment': {
                    'isNormal': False,
                    'confidence': 'low',
                    'summary': 'Insufficient data for normality testing'
                }
            }

        # Determine which tests to run
        if 'auto' in tests_to_run:
            if n <= 50:
                selected_tests = ['shapiroWilk', 'jarqueBera']
            elif n <= 5000:
                selected_tests = ['kolmogorovSmirnov', 'jarqueBera', 'andersonDarling']
            else:
                selected_tests = ['jarqueBera']
        else:
            selected_tests = tests_to_run

        # Determine recommended test
        if n <= 50:
            recommended_test = 'Shapiro-Wilk'
        elif n <= 5000:
            recommended_test = 'Kolmogorov-Smirnov'
        else:
            recommended_test = 'Jarque-Bera'

        # Run selected tests
        tests = {}

        if 'shapiroWilk' in selected_tests:
            tests['shapiroWilk'] = shapiro_wilk_test(data, alpha)

        if 'kolmogorovSmirnov' in selected_tests:
            tests['kolmogorovSmirnov'] = kolmogorov_smirnov_test(data, alpha)

        if 'jarqueBera' in selected_tests:
            tests['jarqueBera'] = jarque_bera_test(data, alpha)

        if 'andersonDarling' in selected_tests:
            tests['andersonDarling'] = anderson_darling_test(data, alpha)

        # Overall assessment
        normal_count = sum(1 for test in tests.values() if test.get('isNormal', False))
        total_tests = len(tests)

        if total_tests == 0:
            is_normal = False
            confidence = 'low'
            summary = 'No tests could be performed'
        elif normal_count == total_tests:
            is_normal = True
            confidence = 'high' if total_tests >= 2 else 'medium'
            summary = f'All {total_tests} test(s) indicate normal distribution'
        elif normal_count == 0:
            is_normal = False
            confidence = 'high' if total_tests >= 2 else 'medium'
            summary = f'All {total_tests} test(s) reject normality'
        else:
            is_normal = normal_count > total_tests / 2
            confidence = 'medium'
            summary = f'{normal_count} of {total_tests} tests indicate normal distribution'

        return {
            'sampleSize': n,
            'recommendedTest': recommended_test,
            'tests': tests,
            'overallAssessment': {
                'isNormal': is_normal,
                'confidence': confidence,
                'summary': summary
            }
        }

    except Exception as e:
        return {
            'sampleSize': len(data) if 'data' in locals() else 0,
            'recommendedTest': 'None',
            'tests': {},
            'overallAssessment': {
                'isNormal': False,
                'confidence': 'low',
                'summary': f'Error in comprehensive normality test: {str(e)}'
            }
        }

def convert_to_json_serializable(obj):
    """
    Convert numpy types to JSON-serializable Python types
    """
    if hasattr(obj, 'item'):  # numpy scalar
        return obj.item()
    elif isinstance(obj, dict):
        return {k: convert_to_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(v) for v in obj]
    else:
        return obj

def run_normality_test(test_type, data, alpha=0.05):
    """
    Run a specific normality test
    """
    if test_type == 'shapiroWilk':
        result = shapiro_wilk_test(data, alpha)
    elif test_type == 'kolmogorovSmirnov':
        result = kolmogorov_smirnov_test(data, alpha)
    elif test_type == 'jarqueBera':
        result = jarque_bera_test(data, alpha)
    elif test_type == 'andersonDarling':
        result = anderson_darling_test(data, alpha)
    elif test_type == 'comprehensive':
        result = comprehensive_normality_test(data, alpha, ['auto'])
    else:
        raise ValueError(f"Unknown test type: {test_type}")

    return convert_to_json_serializable(result)
    `);
  }

  /**
   * Run Shapiro-Wilk test for normality
   */
  async shapiroWilkTest(data: number[], alpha: number = 0.05): Promise<NormalityTestResult> {
    await this.initialize();

    this.pyodide.globals.set('test_data', data);
    this.pyodide.globals.set('test_alpha', alpha);

    const resultJson = this.pyodide.runPython(`
import json
result = shapiro_wilk_test(test_data, test_alpha)
json.dumps(convert_to_json_serializable(result))
    `);

    return JSON.parse(resultJson);
  }

  /**
   * Run Kolmogorov-Smirnov test for normality
   */
  async kolmogorovSmirnovTest(data: number[], alpha: number = 0.05): Promise<NormalityTestResult> {
    await this.initialize();

    this.pyodide.globals.set('test_data', data);
    this.pyodide.globals.set('test_alpha', alpha);

    const resultJson = this.pyodide.runPython(`
import json
result = kolmogorov_smirnov_test(test_data, test_alpha)
json.dumps(convert_to_json_serializable(result))
    `);

    return JSON.parse(resultJson);
  }

  /**
   * Run Jarque-Bera test for normality
   */
  async jarqueBeraTest(data: number[], alpha: number = 0.05): Promise<NormalityTestResult> {
    await this.initialize();

    this.pyodide.globals.set('test_data', data);
    this.pyodide.globals.set('test_alpha', alpha);

    const resultJson = this.pyodide.runPython(`
import json
result = jarque_bera_test(test_data, test_alpha)
json.dumps(convert_to_json_serializable(result))
    `);

    return JSON.parse(resultJson);
  }

  /**
   * Run Anderson-Darling test for normality
   */
  async andersonDarlingTest(data: number[], alpha: number = 0.05): Promise<NormalityTestResult> {
    await this.initialize();

    this.pyodide.globals.set('test_data', data);
    this.pyodide.globals.set('test_alpha', alpha);

    const resultJson = this.pyodide.runPython(`
import json
result = anderson_darling_test(test_data, test_alpha)
json.dumps(convert_to_json_serializable(result))
    `);

    return JSON.parse(resultJson);
  }

  /**
   * Run comprehensive normality testing
   */
  async comprehensiveNormalityTest(
    data: number[],
    alpha: number = 0.05,
    testsToRun: string[] = ['auto']
  ): Promise<ComprehensiveNormalityResult> {
    await this.initialize();

    this.pyodide.globals.set('test_data', data);
    this.pyodide.globals.set('test_alpha', alpha);
    this.pyodide.globals.set('tests_to_run', testsToRun);

    const resultJson = this.pyodide.runPython(`
import json
result = comprehensive_normality_test(test_data, test_alpha, tests_to_run)
json.dumps(convert_to_json_serializable(result))
    `);

    return JSON.parse(resultJson);
  }

  /**
   * Legacy function for backward compatibility
   */
  async isNormallyDistributed(data: number[]): Promise<{
    isNormal: boolean;
    pValue: number;
    statistic: number;
  }> {
    const result = await this.comprehensiveNormalityTest(data, 0.05, ['auto']);

    // Return the result from the recommended test
    const recommendedTestName = result.recommendedTest.toLowerCase().replace(/[^a-z]/g, '');
    let testResult: NormalityTestResult | undefined;

    if (recommendedTestName.includes('shapiro')) {
      testResult = result.tests.shapiroWilk;
    } else if (recommendedTestName.includes('kolmogorov')) {
      testResult = result.tests.kolmogorovSmirnov;
    } else if (recommendedTestName.includes('jarque')) {
      testResult = result.tests.jarqueBera;
    } else if (recommendedTestName.includes('anderson')) {
      testResult = result.tests.andersonDarling;
    }

    if (testResult) {
      return {
        isNormal: testResult.isNormal,
        pValue: testResult.pValue,
        statistic: testResult.statistic
      };
    }

    // Fallback to overall assessment
    return {
      isNormal: result.overallAssessment.isNormal,
      pValue: NaN,
      statistic: NaN
    };
  }

  isReady(): boolean {
    return this.isInitialized;
  }
}

// Create singleton instance
export const normalityTestService = new NormalityTestService();

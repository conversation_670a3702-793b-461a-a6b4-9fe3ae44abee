/**
 * Debug utilities for normality testing
 * 
 * This file provides simple functions that can be called from the browser console
 * to test and debug the normality testing functionality.
 */

import { shapiroWilkTest, comprehensiveNormalityTest } from '../stats/normality';

/**
 * Simple test function that can be called from browser console
 */
export const testNormalityQuick = async (): Promise<void> => {
  console.log('🧪 Quick Normality Test Debug');
  console.log('=' .repeat(50));
  
  try {
    // Generate simple test data
    const testData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    console.log('📊 Test data:', testData);
    
    // Test Shapiro-Wilk
    console.log('\n🔬 Testing Shapiro-Wilk...');
    const swResult = await shapiroWilkTest(testData, 0.05);
    console.log('Result:', swResult);
    
    // Test comprehensive
    console.log('\n🔬 Testing Comprehensive Normality Test...');
    const compResult = await comprehensiveNormalityTest(testData, 0.05, ['auto']);
    console.log('Result:', compResult);
    
    console.log('\n✅ Quick test completed successfully!');
    
  } catch (error) {
    console.error('❌ Quick test failed:', error);
  }
};

/**
 * Test with normal data
 */
export const testWithNormalData = async (): Promise<void> => {
  console.log('🧪 Testing with Normal Data');
  console.log('=' .repeat(50));
  
  try {
    // Generate normal-ish data
    const normalData = [];
    for (let i = 0; i < 30; i++) {
      // Simple normal-ish distribution
      const u1 = Math.random();
      const u2 = Math.random();
      const z = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
      normalData.push(z);
    }
    
    console.log('📊 Normal data sample (first 10):', normalData.slice(0, 10));
    
    const result = await comprehensiveNormalityTest(normalData, 0.05, ['auto']);
    console.log('📈 Comprehensive test result:', result);
    
    console.log('\n✅ Normal data test completed!');
    
  } catch (error) {
    console.error('❌ Normal data test failed:', error);
  }
};

/**
 * Setup global debug functions
 */
export const setupNormalityDebug = (): void => {
  if (typeof window !== 'undefined') {
    (window as any).testNormalityQuick = testNormalityQuick;
    (window as any).testWithNormalData = testWithNormalData;
    
    console.log('🔧 Normality debug functions setup:');
    console.log('   - testNormalityQuick()');
    console.log('   - testWithNormalData()');
    console.log('');
    console.log('💡 You can now call these functions from the browser console!');
  }
};

// Auto-setup if in browser
if (typeof window !== 'undefined') {
  setupNormalityDebug();
}
